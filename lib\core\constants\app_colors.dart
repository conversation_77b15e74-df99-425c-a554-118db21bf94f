import 'package:flutter/material.dart';

/// App color constants following Material Design 3 guidelines
/// with a blue theme for BlueCalc Lite
class AppColors {
  // Primary blue colors
  static const Color primary = Color(0xFF2196F3);
  static const Color primaryDark = Color(0xFF1976D2);
  static const Color primaryLight = Color(0xFF64B5F6);
  
  // Secondary colors
  static const Color secondary = Color(0xFF03DAC6);
  static const Color secondaryDark = Color(0xFF018786);
  
  // Surface colors
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceDark = Color(0xFF121212);
  static const Color surfaceVariant = Color(0xFFF5F5F5);
  
  // Background colors
  static const Color background = Color(0xFFFFFFFF);
  static const Color backgroundDark = Color(0xFF121212);
  
  // Text colors
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color onSecondary = Color(0xFF000000);
  static const Color onSurface = Color(0xFF000000);
  static const Color onSurfaceDark = Color(0xFFFFFFFF);
  static const Color onBackground = Color(0xFF000000);
  static const Color onBackgroundDark = Color(0xFFFFFFFF);
  
  // Calculator specific colors
  static const Color calculatorButton = Color(0xFFF5F5F5);
  static const Color calculatorButtonPressed = Color(0xFFE0E0E0);
  static const Color operatorButton = Color(0xFF2196F3);
  static const Color operatorButtonPressed = Color(0xFF1976D2);
  static const Color equalsButton = Color(0xFF4CAF50);
  static const Color equalsButtonPressed = Color(0xFF388E3C);
  static const Color clearButton = Color(0xFFF44336);
  static const Color clearButtonPressed = Color(0xFFD32F2F);
  
  // Display colors
  static const Color displayBackground = Color(0xFFFAFAFA);
  static const Color displayText = Color(0xFF212121);
  
  // Error and warning colors
  static const Color error = Color(0xFFB00020);
  static const Color warning = Color(0xFFFF9800);
  
  // Gradient colors for splash screen
  static const List<Color> splashGradient = [
    Color(0xFF2196F3),
    Color(0xFF1976D2),
    Color(0xFF0D47A1),
  ];
}
