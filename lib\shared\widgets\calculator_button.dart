import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_constants.dart';

/// Calculator button widget with different styles for different button types
class CalculatorButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final CalculatorButtonType type;
  final bool isPressed;

  const CalculatorButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.type = CalculatorButtonType.number,
    this.isPressed = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(4.0),
      child: Material(
        color: _getButtonColor(),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        elevation: isPressed ? 0 : 2,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          child: Container(
            height: AppConstants.calculatorButtonSize,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              border: Border.all(
                color: _getBorderColor(),
                width: 1,
              ),
            ),
            child: Center(
              child: Text(
                text,
                style: TextStyle(
                  fontSize: _getFontSize(),
                  fontWeight: FontWeight.w600,
                  color: _getTextColor(),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Color _getButtonColor() {
    if (isPressed) {
      switch (type) {
        case CalculatorButtonType.number:
          return AppColors.calculatorButtonPressed;
        case CalculatorButtonType.operator:
          return AppColors.operatorButtonPressed;
        case CalculatorButtonType.equals:
          return AppColors.equalsButtonPressed;
        case CalculatorButtonType.clear:
          return AppColors.clearButtonPressed;
      }
    }

    switch (type) {
      case CalculatorButtonType.number:
        return AppColors.calculatorButton;
      case CalculatorButtonType.operator:
        return AppColors.operatorButton;
      case CalculatorButtonType.equals:
        return AppColors.equalsButton;
      case CalculatorButtonType.clear:
        return AppColors.clearButton;
    }
  }

  Color _getBorderColor() {
    switch (type) {
      case CalculatorButtonType.number:
        return AppColors.calculatorButton.withOpacity(0.3);
      case CalculatorButtonType.operator:
        return AppColors.operatorButton.withOpacity(0.3);
      case CalculatorButtonType.equals:
        return AppColors.equalsButton.withOpacity(0.3);
      case CalculatorButtonType.clear:
        return AppColors.clearButton.withOpacity(0.3);
    }
  }

  Color _getTextColor() {
    switch (type) {
      case CalculatorButtonType.number:
        return AppColors.onSurface;
      case CalculatorButtonType.operator:
      case CalculatorButtonType.equals:
      case CalculatorButtonType.clear:
        return AppColors.onPrimary;
    }
  }

  double _getFontSize() {
    // Larger font for single character operators
    if (text.length == 1 && type == CalculatorButtonType.operator) {
      return 28;
    }
    // Smaller font for longer text like "AC"
    if (text.length > 1) {
      return 18;
    }
    // Default size for numbers
    return 24;
  }
}

/// Enum for different calculator button types
enum CalculatorButtonType {
  number,
  operator,
  equals,
  clear,
}
