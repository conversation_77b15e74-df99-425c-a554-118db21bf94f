import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_constants.dart';

/// Calculator button widget with different styles for different button types
class CalculatorButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final CalculatorButtonType type;
  final bool isPressed;

  const CalculatorButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.type = CalculatorButtonType.number,
    this.isPressed = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(4.0),
      child: Material(
        color: _getButtonColor(context),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        elevation: isPressed ? 0 : 2,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          child: Container(
            height: AppConstants.calculatorButtonSize,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              border: Border.all(
                color: _getBorderColor(context),
                width: 1,
              ),
            ),
            child: Center(
              child: Text(
                text,
                style: TextStyle(
                  fontSize: _getFontSize(),
                  fontWeight: FontWeight.w600,
                  color: _getTextColor(context),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Color _getButtonColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    if (isPressed) {
      switch (type) {
        case CalculatorButtonType.number:
          return isDark ? AppColors.calculatorButtonPressedDark : AppColors.calculatorButtonPressed;
        case CalculatorButtonType.operator:
          return isDark ? AppColors.operatorButtonPressedDark : AppColors.operatorButtonPressed;
        case CalculatorButtonType.equals:
          return isDark ? AppColors.equalsButtonPressedDark : AppColors.equalsButtonPressed;
        case CalculatorButtonType.clear:
          return isDark ? AppColors.clearButtonPressedDark : AppColors.clearButtonPressed;
      }
    }

    switch (type) {
      case CalculatorButtonType.number:
        return isDark ? AppColors.calculatorButtonDark : AppColors.calculatorButton;
      case CalculatorButtonType.operator:
        return isDark ? AppColors.operatorButtonDark : AppColors.operatorButton;
      case CalculatorButtonType.equals:
        return isDark ? AppColors.equalsButtonDark : AppColors.equalsButton;
      case CalculatorButtonType.clear:
        return isDark ? AppColors.clearButtonDark : AppColors.clearButton;
    }
  }

  Color _getBorderColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    switch (type) {
      case CalculatorButtonType.number:
        final baseColor = isDark ? AppColors.calculatorButtonDark : AppColors.calculatorButton;
        return baseColor.withValues(alpha: 0.3);
      case CalculatorButtonType.operator:
        final baseColor = isDark ? AppColors.operatorButtonDark : AppColors.operatorButton;
        return baseColor.withValues(alpha: 0.3);
      case CalculatorButtonType.equals:
        final baseColor = isDark ? AppColors.equalsButtonDark : AppColors.equalsButton;
        return baseColor.withValues(alpha: 0.3);
      case CalculatorButtonType.clear:
        final baseColor = isDark ? AppColors.clearButtonDark : AppColors.clearButton;
        return baseColor.withValues(alpha: 0.3);
    }
  }

  Color _getTextColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    switch (type) {
      case CalculatorButtonType.number:
        return isDark ? AppColors.onSurfaceDark : AppColors.onSurface;
      case CalculatorButtonType.operator:
      case CalculatorButtonType.equals:
      case CalculatorButtonType.clear:
        return AppColors.onPrimary;
    }
  }

  double _getFontSize() {
    // Larger font for single character operators
    if (text.length == 1 && type == CalculatorButtonType.operator) {
      return 28;
    }
    // Smaller font for longer text like "AC"
    if (text.length > 1) {
      return 18;
    }
    // Default size for numbers
    return 24;
  }
}

/// Enum for different calculator button types
enum CalculatorButtonType {
  number,
  operator,
  equals,
  clear,
}
