/// App-wide constants for BlueCalc Lite
class AppConstants {
  // App information
  static const String appName = 'BlueCalc Lite';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Privacy-First Offline Calculator';
  
  // Shared Preferences keys
  static const String keyOnboardingCompleted = 'onboarding_completed';
  static const String keyFirstLaunch = 'first_launch';
  
  // Animation durations
  static const Duration splashDuration = Duration(seconds: 3);
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration fastAnimationDuration = Duration(milliseconds: 150);
  
  // UI dimensions
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double extraLargePadding = 32.0;
  
  static const double buttonHeight = 56.0;
  static const double calculatorButtonSize = 72.0;
  static const double borderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  
  // Calculator constants
  static const int maxDisplayDigits = 12;
  static const String defaultDisplayValue = '0';
  static const String errorMessage = 'Error';
  static const String divisionByZeroMessage = 'Cannot divide by zero';
  
  // Onboarding content
  static const List<OnboardingContent> onboardingPages = [
    OnboardingContent(
      title: 'Welcome to BlueCalc Lite',
      description: 'Your simple, elegant calculator that respects your privacy',
      icon: 'calculator',
    ),
    OnboardingContent(
      title: 'Simple & Private',
      description: 'No data collection, no internet required. Just pure calculation power.',
      icon: 'privacy',
    ),
    OnboardingContent(
      title: 'Ready to Calculate',
      description: 'Start calculating with confidence. Your data stays on your device.',
      icon: 'ready',
    ),
  ];
  
  // Privacy policy text
  static const String privacyPolicyText = '''
Privacy Policy for BlueCalc Lite

BlueCalc Lite is completely offline and privacy-first by design.

DATA COLLECTION:
• We do not collect any personal data
• We do not collect any usage analytics
• We do not collect any device information
• We do not track your calculations or usage patterns

DATA STORAGE:
• All calculations are performed locally on your device
• No calculation history is stored permanently
• Only basic app preferences (like onboarding completion) are saved locally

INTERNET ACCESS:
• This app does not require internet connection
• No data is transmitted to external servers
• No third-party services are integrated

PERMISSIONS:
• This app requests no special permissions
• No access to your contacts, location, camera, or other sensitive data

Your privacy is our priority. BlueCalc Lite is designed to be a simple, offline calculator that respects your digital privacy completely.

Last updated: January 2025
''';
}

/// Onboarding content model
class OnboardingContent {
  final String title;
  final String description;
  final String icon;
  
  const OnboardingContent({
    required this.title,
    required this.description,
    required this.icon,
  });
}
