// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';


import 'package:bluecalc_lite/features/calculator/calculator_provider.dart';
import 'package:bluecalc_lite/features/calculator/calculator_screen.dart';

void main() {
  group('BlueCalc Lite App Tests', () {
    testWidgets('Calculator screen loads correctly', (WidgetTester tester) async {
      // Create calculator screen directly for testing
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider(
            create: (context) => CalculatorProvider(),
            child: const CalculatorScreen(),
          ),
        ),
      );

      // Verify calculator screen loads
      expect(find.byType(CalculatorScreen), findsOneWidget);
      expect(find.text('BlueCalc Lite'), findsOneWidget);

      // Verify calculator buttons are present
      expect(find.text('1'), findsOneWidget);
      expect(find.text('2'), findsOneWidget);
      expect(find.text('+'), findsOneWidget);
      expect(find.text('='), findsOneWidget);
      expect(find.text('AC'), findsOneWidget);
    });
  });

  group('Calculator Provider Tests', () {
    late CalculatorProvider calculator;

    setUp(() {
      calculator = CalculatorProvider();
    });

    test('Initial state should be 0', () {
      expect(calculator.display, '0');
      expect(calculator.hasError, false);
    });

    test('Number input should work correctly', () {
      calculator.inputNumber('1');
      expect(calculator.display, '1');

      calculator.inputNumber('2');
      expect(calculator.display, '12');

      calculator.inputNumber('3');
      expect(calculator.display, '123');
    });

    test('Decimal input should work correctly', () {
      calculator.inputNumber('1');
      calculator.inputDecimal();
      calculator.inputNumber('5');
      expect(calculator.display, '1.5');

      // Should not add multiple decimals
      calculator.inputDecimal();
      expect(calculator.display, '1.5');
    });

    test('Basic arithmetic operations should work', () {
      // Test addition
      calculator.inputNumber('5');
      calculator.inputOperation('+');
      calculator.inputNumber('3');
      calculator.calculate();
      expect(calculator.display, '8');

      // Test subtraction
      calculator.allClear();
      calculator.inputNumber('10');
      calculator.inputOperation('-');
      calculator.inputNumber('4');
      calculator.calculate();
      expect(calculator.display, '6');

      // Test multiplication
      calculator.allClear();
      calculator.inputNumber('6');
      calculator.inputOperation('×');
      calculator.inputNumber('7');
      calculator.calculate();
      expect(calculator.display, '42');

      // Test division
      calculator.allClear();
      calculator.inputNumber('15');
      calculator.inputOperation('÷');
      calculator.inputNumber('3');
      calculator.calculate();
      expect(calculator.display, '5');
    });

    test('Division by zero should show error', () {
      calculator.inputNumber('5');
      calculator.inputOperation('÷');
      calculator.inputNumber('0');
      calculator.calculate();
      expect(calculator.hasError, true);
      expect(calculator.display, 'Cannot divide by zero');
    });

    test('Clear functions should work correctly', () {
      calculator.inputNumber('123');
      calculator.clear();
      expect(calculator.display, '0');

      calculator.inputNumber('456');
      calculator.inputOperation('+');
      calculator.inputNumber('789');
      calculator.allClear();
      expect(calculator.display, '0');
    });

    test('Sign toggle should work correctly', () {
      calculator.inputNumber('5');
      calculator.toggleSign();
      expect(calculator.display, '-5');

      calculator.toggleSign();
      expect(calculator.display, '5');
    });

    test('Percentage should work correctly', () {
      calculator.inputNumber('50');
      calculator.percentage();
      expect(calculator.display, '0.5');
    });

    test('Chained operations should work correctly', () {
      calculator.inputNumber('2');
      calculator.inputOperation('+');
      calculator.inputNumber('3');
      calculator.inputOperation('×');
      calculator.inputNumber('4');
      calculator.calculate();
      expect(calculator.display, '20'); // (2 + 3) × 4 = 20
    });
  });
}
