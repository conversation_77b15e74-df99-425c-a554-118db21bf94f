import 'package:flutter/foundation.dart';
import '../../core/constants/app_constants.dart';

/// Calculator state management using Provider
/// Handles all calculator operations and display logic
class CalculatorProvider extends ChangeNotifier {
  String _display = AppConstants.defaultDisplayValue;
  String _previousValue = '';
  String _operation = '';
  bool _waitingForOperand = false;
  bool _hasError = false;

  String get display => _display;
  bool get hasError => _hasError;

  /// Input a number digit
  void inputNumber(String number) {
    if (_hasError) {
      _clear();
    }

    if (_waitingForOperand) {
      _display = number;
      _waitingForOperand = false;
    } else {
      if (_display == AppConstants.defaultDisplayValue) {
        _display = number;
      } else {
        // Check if we're at max digits
        if (_display.replaceAll('.', '').length < AppConstants.maxDisplayDigits) {
          _display += number;
        }
      }
    }
    notifyListeners();
  }

  /// Input decimal point
  void inputDecimal() {
    if (_hasError) {
      _clear();
    }

    if (_waitingForOperand) {
      _display = '0.';
      _waitingForOperand = false;
    } else if (!_display.contains('.')) {
      _display += '.';
    }
    notifyListeners();
  }

  /// Input an operation (+, -, ×, ÷)
  void inputOperation(String nextOperation) {
    if (_hasError) {
      return;
    }

    double inputValue = double.tryParse(_display) ?? 0;

    if (_previousValue.isEmpty) {
      _previousValue = inputValue.toString();
    } else if (_operation.isNotEmpty) {
      double previousVal = double.tryParse(_previousValue) ?? 0;
      double result = _calculate(previousVal, inputValue, _operation);
      
      if (result.isInfinite || result.isNaN) {
        _showError(AppConstants.divisionByZeroMessage);
        return;
      }
      
      _display = _formatResult(result);
      _previousValue = result.toString();
    }

    _waitingForOperand = true;
    _operation = nextOperation;
    notifyListeners();
  }

  /// Calculate result (equals button)
  void calculate() {
    if (_hasError || _operation.isEmpty || _previousValue.isEmpty) {
      return;
    }

    double inputValue = double.tryParse(_display) ?? 0;
    double previousVal = double.tryParse(_previousValue) ?? 0;
    double result = _calculate(previousVal, inputValue, _operation);

    if (result.isInfinite || result.isNaN) {
      _showError(AppConstants.divisionByZeroMessage);
      return;
    }

    _display = _formatResult(result);
    _previousValue = '';
    _operation = '';
    _waitingForOperand = true;
    notifyListeners();
  }

  /// Clear current entry
  void clear() {
    _display = AppConstants.defaultDisplayValue;
    notifyListeners();
  }

  /// Clear all (reset calculator)
  void allClear() {
    _clear();
    notifyListeners();
  }

  /// Toggle positive/negative
  void toggleSign() {
    if (_hasError) {
      return;
    }

    if (_display != AppConstants.defaultDisplayValue) {
      if (_display.startsWith('-')) {
        _display = _display.substring(1);
      } else {
        _display = '-$_display';
      }
      notifyListeners();
    }
  }

  /// Calculate percentage
  void percentage() {
    if (_hasError) {
      return;
    }

    double value = double.tryParse(_display) ?? 0;
    double result = value / 100;
    _display = _formatResult(result);
    notifyListeners();
  }

  /// Perform the actual calculation
  double _calculate(double firstOperand, double secondOperand, String operation) {
    switch (operation) {
      case '+':
        return firstOperand + secondOperand;
      case '-':
        return firstOperand - secondOperand;
      case '×':
        return firstOperand * secondOperand;
      case '÷':
        if (secondOperand == 0) {
          return double.infinity;
        }
        return firstOperand / secondOperand;
      default:
        return secondOperand;
    }
  }

  /// Format the result for display
  String _formatResult(double result) {
    // Handle very large or very small numbers
    if (result.abs() >= 1e12 || (result.abs() < 1e-6 && result != 0)) {
      return result.toStringAsExponential(6);
    }

    // Remove unnecessary decimal places
    if (result == result.roundToDouble()) {
      return result.toInt().toString();
    }

    String formatted = result.toString();
    
    // Limit decimal places to fit display
    if (formatted.length > AppConstants.maxDisplayDigits) {
      int decimalPlaces = AppConstants.maxDisplayDigits - formatted.indexOf('.') - 1;
      if (decimalPlaces > 0) {
        formatted = result.toStringAsFixed(decimalPlaces);
        // Remove trailing zeros
        formatted = formatted.replaceAll(RegExp(r'0*$'), '').replaceAll(RegExp(r'\.$'), '');
      }
    }

    return formatted;
  }

  /// Show error message
  void _showError(String message) {
    _display = message;
    _hasError = true;
    _previousValue = '';
    _operation = '';
    _waitingForOperand = false;
  }

  /// Reset calculator state
  void _clear() {
    _display = AppConstants.defaultDisplayValue;
    _previousValue = '';
    _operation = '';
    _waitingForOperand = false;
    _hasError = false;
  }
}
