import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_constants.dart';
import '../../shared/widgets/calculator_button.dart';
import '../privacy/privacy_policy_screen.dart';
import 'calculator_provider.dart';

/// Main calculator screen with display and button grid
class CalculatorScreen extends StatelessWidget {
  const CalculatorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => CalculatorProvider(),
      child: const _CalculatorView(),
    );
  }
}

class _CalculatorView extends StatelessWidget {
  const _CalculatorView();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(AppConstants.appName),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.onPrimary,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const PrivacyPolicyScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Display area
          Expanded(
            flex: 2,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.largePadding),
              decoration: const BoxDecoration(
                color: AppColors.displayBackground,
                border: Border(
                  bottom: BorderSide(
                    color: AppColors.primary,
                    width: 2,
                  ),
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Consumer<CalculatorProvider>(
                    builder: (context, calculator, child) {
                      return Text(
                        calculator.display,
                        style: Theme.of(context).textTheme.displayLarge?.copyWith(
                          color: calculator.hasError 
                              ? AppColors.error 
                              : AppColors.displayText,
                          fontWeight: FontWeight.w300,
                        ),
                        textAlign: TextAlign.right,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
          
          // Button grid
          Expanded(
            flex: 3,
            child: Container(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Consumer<CalculatorProvider>(
                builder: (context, calculator, child) {
                  return Column(
                    children: [
                      // Row 1: AC, C, ±, ÷
                      Expanded(
                        child: Row(
                          children: [
                            Expanded(
                              child: CalculatorButton(
                                text: 'AC',
                                type: CalculatorButtonType.clear,
                                onPressed: calculator.allClear,
                              ),
                            ),
                            Expanded(
                              child: CalculatorButton(
                                text: 'C',
                                type: CalculatorButtonType.clear,
                                onPressed: calculator.clear,
                              ),
                            ),
                            Expanded(
                              child: CalculatorButton(
                                text: '±',
                                type: CalculatorButtonType.operator,
                                onPressed: calculator.toggleSign,
                              ),
                            ),
                            Expanded(
                              child: CalculatorButton(
                                text: '÷',
                                type: CalculatorButtonType.operator,
                                onPressed: () => calculator.inputOperation('÷'),
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Row 2: 7, 8, 9, ×
                      Expanded(
                        child: Row(
                          children: [
                            Expanded(
                              child: CalculatorButton(
                                text: '7',
                                onPressed: () => calculator.inputNumber('7'),
                              ),
                            ),
                            Expanded(
                              child: CalculatorButton(
                                text: '8',
                                onPressed: () => calculator.inputNumber('8'),
                              ),
                            ),
                            Expanded(
                              child: CalculatorButton(
                                text: '9',
                                onPressed: () => calculator.inputNumber('9'),
                              ),
                            ),
                            Expanded(
                              child: CalculatorButton(
                                text: '×',
                                type: CalculatorButtonType.operator,
                                onPressed: () => calculator.inputOperation('×'),
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Row 3: 4, 5, 6, -
                      Expanded(
                        child: Row(
                          children: [
                            Expanded(
                              child: CalculatorButton(
                                text: '4',
                                onPressed: () => calculator.inputNumber('4'),
                              ),
                            ),
                            Expanded(
                              child: CalculatorButton(
                                text: '5',
                                onPressed: () => calculator.inputNumber('5'),
                              ),
                            ),
                            Expanded(
                              child: CalculatorButton(
                                text: '6',
                                onPressed: () => calculator.inputNumber('6'),
                              ),
                            ),
                            Expanded(
                              child: CalculatorButton(
                                text: '-',
                                type: CalculatorButtonType.operator,
                                onPressed: () => calculator.inputOperation('-'),
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Row 4: 1, 2, 3, +
                      Expanded(
                        child: Row(
                          children: [
                            Expanded(
                              child: CalculatorButton(
                                text: '1',
                                onPressed: () => calculator.inputNumber('1'),
                              ),
                            ),
                            Expanded(
                              child: CalculatorButton(
                                text: '2',
                                onPressed: () => calculator.inputNumber('2'),
                              ),
                            ),
                            Expanded(
                              child: CalculatorButton(
                                text: '3',
                                onPressed: () => calculator.inputNumber('3'),
                              ),
                            ),
                            Expanded(
                              child: CalculatorButton(
                                text: '+',
                                type: CalculatorButtonType.operator,
                                onPressed: () => calculator.inputOperation('+'),
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Row 5: 0, ., %,  =
                      Expanded(
                        child: Row(
                          children: [
                            Expanded(
                              child: CalculatorButton(
                                text: '0',
                                onPressed: () => calculator.inputNumber('0'),
                              ),
                            ),
                            Expanded(
                              child: CalculatorButton(
                                text: '.',
                                onPressed: calculator.inputDecimal,
                              ),
                            ),
                            Expanded(
                              child: CalculatorButton(
                                text: '%',
                                type: CalculatorButtonType.operator,
                                onPressed: calculator.percentage,
                              ),
                            ),
                            Expanded(
                              child: CalculatorButton(
                                text: '=',
                                type: CalculatorButtonType.equals,
                                onPressed: calculator.calculate,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
