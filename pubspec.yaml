name: bluecalc_lite
description: "BlueCalc Lite - Privacy-First Offline Calculator. A simple, elegant calculator app that works completely offline with zero data collection."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # Core dependencies for offline calculator functionality
  cupertino_icons: ^1.0.8
  shared_preferences: ^2.2.2
  provider: ^6.1.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_native_splash: ^2.3.10
  flutter_launcher_icons: ^0.14.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # App assets
  assets:
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Flutter Native Splash configuration
flutter_native_splash:
  color: "#2196F3"
  color_dark: "#1976D2"
  # Using built-in icon for now - can be replaced with custom icon later
  android_12:
    color: "#2196F3"
    color_dark: "#1976D2"

# Flutter Launcher Icons configuration
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/calculator-icon.png"
  min_sdk_android: 21 # Android 5.0
  remove_alpha_ios: true # Remove alpha channel for App Store compliance
  web:
    generate: true
    image_path: "assets/images/calculator-icon.png"
    background_color: "#2196F3"
    theme_color: "#2196F3"
  windows:
    generate: true
    image_path: "assets/images/calculator-icon.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/images/calculator-icon.png"
